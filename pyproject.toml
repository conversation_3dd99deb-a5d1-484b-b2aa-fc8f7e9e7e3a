[build-system]
requires = [
    "setuptools>=64.0.0",
    "wheel",
    "torch>=1.8.0",
    "torchvision"
]
build-backend = "setuptools.build_meta"

[project]
name = "detrex"
version = "0.3.0"
description = "IDEA open source toolbox for transformer-based instance recognition tasks"
authors = [
    {name = "International Digital Economy Academy"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "cloudpickle",
    "hydra-core",
    "omegaconf",
    "pybind11",
    "flake8",
    "isort",
    "black",
    "autoflake",
    "timm",
    "pytest",
    "scipy",
    "psutil",
    "opencv-python",
    "wandb",
    "submitit",
    "einops",
    "fairscale"
]

[project.urls]
Homepage = "https://github.com/rentainhe/detrex"
