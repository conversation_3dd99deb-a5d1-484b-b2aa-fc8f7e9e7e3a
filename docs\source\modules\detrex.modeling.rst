detrex.modeling
##############################

backbone
------------------------------
.. currentmodule:: detrex.modeling
.. automodule:: detrex.modeling.backbone
    :member-order: bysource
    :members:
        ResNet,
        make_stage,
        ConvNeXt,
        FocalNet,
        TimmBackbone,
        TorchvisionBackbone,

neck
------------------------------
.. currentmodule:: detrex.modeling
.. automodule:: detrex.modeling.neck
    :member-order: bysource
    :members:
        <PERSON><PERSON><PERSON><PERSON>,


matcher
------------------------------
.. currentmodule:: detrex.modeling
.. automodule:: detrex.modeling.matcher
    :member-order: bysource
    :members:
        <PERSON>Matcher,


losses
------------------------------
.. currentmodule:: detrex.modeling
.. automodule:: detrex.modeling.losses
    :member-order: bysource
    :members:
        sigmoid_focal_loss,
        dice_loss,