Here are projects that are built on detrex which show you use detrex as a library and make your own projects more maintainable.

## Projects by detrex
- [End-to-End Object Detection with Transformers (ECCV'2020)](./detr)
- [Deformable DETR: Deformable Transformers for End-to-End Object Detection (ICLR'2021 Oral)](./deformable_detr/)
- [PnP-DETR: Towards Efficient Visual Analysis with Transformers (ICCV'2021)](./pnp_detr/)
- [Conditional DETR for Fast Training Convergence (ICCV'2021)](./conditional_detr/)
- [Anchor DETR: Query Design for Transformer-Based Detector (AAAI'2022)](./anchor_detr/)
- [DAB-DETR: Dynamic Anchor Boxes are Better Queries for DETR (ICLR'2022)](./dab_detr/)
- [DN-DETR: Accelerate DETR Training by Introducing Query DeNoising (CVPR'2022 Oral)](./dn_detr/)
- [DINO: DETR with Improved DeNoising Anchor Boxes for End-to-End Object Detection (ICLR'2023)](./dino)
- [Group DETR: Fast DETR Training with Group-Wise One-to-Many Assignment (ICCV'2023)](./group_detr/)
- [DETRs with Hybrid Matching (CVPR'2023)](./h_deformable_detr/)
- [Mask DINO: Towards A Unified Transformer-based Framework for Object Detection and Segmentation (CVPR'2023)](./maskdino/)
- [NMS strikes back (ArXiv'2022)](./deta/)
- [CO-MOT: Bridging the Gap Between End-to-end and Non-End-to-end Multi-Object Tracking (ArXiv'2023)](./co_mot/)
- [Enhanced Training of Query-Based Object Detection via Selective Query Recollection (CVPR'2023)](./sqr_detr/)
- [Align-DETR: Improving DETR with Simple IoU-aware BCE loss (ArXiv'2023)](./align_detr/)
- [EVA-01: Exploring the Limits of Masked Visual Representation Learning at Scale (CVPR'2023 Highlight)](./dino_eva/)
- [EVA-02: A Visual Representation for Neon Genesis (ArXiv'2023)](./dino_eva/)
- [Less is More: Focus Attention for Efficient DETR (ICCV'2023)](./focus_detr/)
