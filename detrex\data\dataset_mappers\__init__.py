# coding=utf-8
# Copyright 2022 The IDEA Authors. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from .coco_instance_new_baseline_dataset_mapper import build_transform_gen as coco_instance_transform_gen
from .coco_panoptic_new_baseline_dataset_mapper import build_transform_gen as coco_panoptic_transform_gen
from .mask_former_semantic_dataset_mapper import build_transform_gen as maskformer_semantic_transform_gen
from .coco_instance_new_baseline_dataset_mapper import COCOInstanceNewBaselineDatasetMapper
from .coco_panoptic_new_baseline_dataset_mapper import COCOPanopticNewBaselineDatasetMapper
from .mask_former_instance_dataset_mapper import MaskFormerInstanceDatasetMapper
from .mask_former_panoptic_dataset_mapper import MaskFormerPanopticDatasetMapper
from .mask_former_semantic_dataset_mapper import MaskFormerSemanticDatasetMapper
