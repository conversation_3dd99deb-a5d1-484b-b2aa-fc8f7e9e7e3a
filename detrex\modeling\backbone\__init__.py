# coding=utf-8
# Copyright 2022 The IDEA Authors. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from .timm_backbone import Timm<PERSON><PERSON>bone
from .torchvision_backbone import TorchvisionBackbone
from .resnet import (
    BasicStem,
    ResNet,
    ResNetBlockBase,
    make_stage,
    BottleneckBlock,
    BasicBlock,
    DeformBottleneckBlock,
)
from .convnext import ConvNeXt
from .focalnet import FocalNet
from .internimage import InternImage
from .eva import EVAViT, SimpleFeaturePyramid, get_vit_lr_decay_rate
from .eva_02 import EVA02_ViT
